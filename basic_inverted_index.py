"""
基础反向索引模块 - 构建反向索引并实现多单词搜索功能
"""
import re
from collections import defaultdict
from typing import List, Dict, Set, Tuple
import math

class Document:
    """文档类"""
    def __init__(self, doc_id: int, content: str, title: str = ""):
        self.doc_id = doc_id
        self.content = content
        self.title = title
        self.words = self._preprocess_content()
        self.word_count = len(self.words)
    
    def _preprocess_content(self) -> List[str]:
        """预处理文档内容"""
        # 转换为小写，移除标点符号
        text = re.sub(r'[^\w\s]', ' ', self.content.lower())
        # 分割单词并过滤空字符串
        words = [word for word in text.split() if word.strip()]
        return words

class PostingList:
    """倒排列表类"""
    def __init__(self):
        self.documents = {}  # {doc_id: [positions]}
        self.document_frequency = 0
    
    def add_document(self, doc_id: int, positions: List[int]):
        """添加文档和位置信息"""
        if doc_id not in self.documents:
            self.document_frequency += 1
        self.documents[doc_id] = positions
    
    def get_documents(self) -> Set[int]:
        """获取包含该词的所有文档ID"""
        return set(self.documents.keys())
    
    def get_positions(self, doc_id: int) -> List[int]:
        """获取词在特定文档中的位置"""
        return self.documents.get(doc_id, [])

class BasicInvertedIndex:
    """基础反向索引"""
    def __init__(self):
        self.index = defaultdict(PostingList)  # {word: PostingList}
        self.documents = {}  # {doc_id: Document}
        self.total_documents = 0
        self.vocabulary = set()
    
    def add_document(self, doc_id: int, content: str, title: str = ""):
        """添加文档到索引"""
        document = Document(doc_id, content, title)
        self.documents[doc_id] = document
        self.total_documents += 1
        
        # 构建倒排索引
        word_positions = defaultdict(list)
        
        for position, word in enumerate(document.words):
            word_positions[word].append(position)
            self.vocabulary.add(word)
        
        # 更新倒排索引
        for word, positions in word_positions.items():
            self.index[word].add_document(doc_id, positions)
    
    def build_index(self, texts: List[str]):
        """批量构建索引"""
        print("正在构建基础反向索引...")
        
        for i, text in enumerate(texts):
            title = f"Document {i+1}"
            self.add_document(i, text, title)
        
        print(f"索引构建完成！")
        print(f"总文档数: {self.total_documents}")
        print(f"词汇表大小: {len(self.vocabulary)}")
    
    def search_single_word(self, word: str) -> Set[int]:
        """搜索单个单词"""
        word = word.lower()
        if word in self.index:
            return self.index[word].get_documents()
        return set()
    
    def search_multiple_words_and(self, words: List[str]) -> Set[int]:
        """多单词AND搜索（所有单词都必须出现）"""
        if not words:
            return set()
        
        # 获取第一个单词的文档集合
        result = self.search_single_word(words[0])
        
        # 与其他单词的文档集合求交集
        for word in words[1:]:
            word_docs = self.search_single_word(word)
            result = result.intersection(word_docs)
            
            # 如果交集为空，提前退出
            if not result:
                break
        
        return result
    
    def search_multiple_words_or(self, words: List[str]) -> Set[int]:
        """多单词OR搜索（任意单词出现即可）"""
        result = set()
        
        for word in words:
            word_docs = self.search_single_word(word)
            result = result.union(word_docs)
        
        return result
    
    def phrase_search(self, phrase: str) -> Set[int]:
        """短语搜索（单词必须连续出现）"""
        words = phrase.lower().split()
        if len(words) == 1:
            return self.search_single_word(words[0])
        
        # 获取包含所有单词的文档
        candidate_docs = self.search_multiple_words_and(words)
        
        result = set()
        
        # 检查每个候选文档中是否包含完整短语
        for doc_id in candidate_docs:
            if self._contains_phrase(doc_id, words):
                result.add(doc_id)
        
        return result
    
    def _contains_phrase(self, doc_id: int, words: List[str]) -> bool:
        """检查文档是否包含完整短语"""
        if not words:
            return False
        
        # 获取第一个单词的位置
        first_word_positions = self.index[words[0]].get_positions(doc_id)
        
        for start_pos in first_word_positions:
            # 检查后续单词是否在连续位置出现
            valid_phrase = True
            for i, word in enumerate(words[1:], 1):
                expected_pos = start_pos + i
                word_positions = self.index[word].get_positions(doc_id)
                
                if expected_pos not in word_positions:
                    valid_phrase = False
                    break
            
            if valid_phrase:
                return True
        
        return False
    
    def calculate_tf_idf(self, word: str, doc_id: int) -> float:
        """计算TF-IDF分数"""
        if word not in self.index or doc_id not in self.documents:
            return 0.0
        
        # 计算TF (Term Frequency)
        word_positions = self.index[word].get_positions(doc_id)
        tf = len(word_positions) / self.documents[doc_id].word_count
        
        # 计算IDF (Inverse Document Frequency)
        df = self.index[word].document_frequency
        idf = math.log(self.total_documents / df) if df > 0 else 0
        
        return tf * idf
    
    def ranked_search(self, query: str, top_k: int = 5) -> List[Tuple[int, float]]:
        """排序搜索（基于TF-IDF分数）"""
        words = query.lower().split()
        doc_scores = defaultdict(float)
        
        # 计算每个文档的总分数
        for word in words:
            if word in self.index:
                for doc_id in self.index[word].get_documents():
                    doc_scores[doc_id] += self.calculate_tf_idf(word, doc_id)
        
        # 按分数排序
        ranked_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        
        return ranked_docs[:top_k]
    
    def get_word_statistics(self, word: str) -> Dict:
        """获取单词统计信息"""
        word = word.lower()
        if word not in self.index:
            return {"exists": False}
        
        posting_list = self.index[word]
        total_occurrences = sum(len(positions) for positions in posting_list.documents.values())
        
        return {
            "exists": True,
            "document_frequency": posting_list.document_frequency,
            "total_occurrences": total_occurrences,
            "documents": list(posting_list.get_documents())
        }
    
    def search_with_details(self, query: str, search_type: str = "and") -> Dict:
        """详细搜索结果"""
        words = query.lower().split()
        
        if search_type == "and":
            result_docs = self.search_multiple_words_and(words)
        elif search_type == "or":
            result_docs = self.search_multiple_words_or(words)
        elif search_type == "phrase":
            result_docs = self.phrase_search(query)
        else:
            result_docs = self.search_multiple_words_and(words)
        
        # 获取详细信息
        results = []
        for doc_id in result_docs:
            doc = self.documents[doc_id]
            
            # 计算查询相关性分数
            score = sum(self.calculate_tf_idf(word, doc_id) for word in words)
            
            # 获取文档片段（包含查询词的上下文）
            snippet = self._get_snippet(doc, words)
            
            results.append({
                "doc_id": doc_id,
                "title": doc.title,
                "score": score,
                "snippet": snippet,
                "word_count": doc.word_count
            })
        
        # 按分数排序
        results.sort(key=lambda x: x["score"], reverse=True)
        
        return {
            "query": query,
            "search_type": search_type,
            "total_results": len(results),
            "results": results
        }
    
    def _get_snippet(self, document: Document, query_words: List[str], context_size: int = 10) -> str:
        """获取包含查询词的文档片段"""
        words = document.words
        
        # 找到第一个查询词的位置
        for word in query_words:
            if word in words:
                try:
                    pos = words.index(word)
                    start = max(0, pos - context_size)
                    end = min(len(words), pos + context_size + 1)
                    snippet_words = words[start:end]
                    
                    # 高亮查询词
                    highlighted = []
                    for w in snippet_words:
                        if w in query_words:
                            highlighted.append(f"**{w}**")
                        else:
                            highlighted.append(w)
                    
                    return " ".join(highlighted)
                except ValueError:
                    continue
        
        # 如果没找到查询词，返回文档开头
        return " ".join(words[:context_size * 2])

if __name__ == "__main__":
    # 示例使用
    from text_collector import TextCollector
    
    # 收集文本
    collector = TextCollector()
    texts = collector.collect_texts(source="sample")
    
    # 构建索引
    index = BasicInvertedIndex()
    index.build_index(texts)
    
    # 测试搜索功能
    print("\n=== 搜索测试 ===")
    
    # 单词搜索
    print("\n1. 单词搜索 'economy':")
    docs = index.search_single_word("economy")
    print(f"找到 {len(docs)} 个文档: {docs}")
    
    # AND搜索
    print("\n2. AND搜索 'artificial intelligence':")
    docs = index.search_multiple_words_and(["artificial", "intelligence"])
    print(f"找到 {len(docs)} 个文档: {docs}")
    
    # OR搜索
    print("\n3. OR搜索 'climate technology':")
    docs = index.search_multiple_words_or(["climate", "technology"])
    print(f"找到 {len(docs)} 个文档: {docs}")
    
    # 短语搜索
    print("\n4. 短语搜索 'climate change':")
    docs = index.phrase_search("climate change")
    print(f"找到 {len(docs)} 个文档: {docs}")
    
    # 排序搜索
    print("\n5. 排序搜索 'artificial intelligence technology':")
    ranked_results = index.ranked_search("artificial intelligence technology", top_k=3)
    for doc_id, score in ranked_results:
        print(f"文档 {doc_id}: 分数 {score:.4f}")
    
    # 详细搜索
    print("\n6. 详细搜索结果:")
    detailed_results = index.search_with_details("climate change", search_type="phrase")
    print(f"查询: {detailed_results['query']}")
    print(f"搜索类型: {detailed_results['search_type']}")
    print(f"结果数量: {detailed_results['total_results']}")
    
    for result in detailed_results['results']:
        print(f"\n文档 {result['doc_id']} ({result['title']}):")
        print(f"分数: {result['score']:.4f}")
        print(f"片段: {result['snippet']}")
    
    # 单词统计
    print("\n7. 单词统计 'technology':")
    stats = index.get_word_statistics("technology")
    print(stats)
