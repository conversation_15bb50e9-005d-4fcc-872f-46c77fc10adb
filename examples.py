"""
使用示例 - 展示如何单独使用各个模块
"""

def example_text_collector():
    """文本收集器使用示例"""
    print("=== 文本收集器示例 ===")
    from text_collector import TextCollector
    
    collector = TextCollector()
    
    # 收集示例文本
    texts = collector.collect_texts(source="sample")
    print(f"收集了 {len(texts)} 篇文本")
    
    # 保存文本
    collector.save_texts("example_texts.txt")
    print("文本已保存到 example_texts.txt")
    
    return texts

def example_collocation_finder(texts):
    """固定搭配查找器使用示例"""
    print("\n=== 固定搭配查找器示例 ===")
    from collocation_finder import CollocationFinder
    
    finder = CollocationFinder()
    finder.analyze_texts(texts)
    
    # 查找高频固定搭配
    collocations = finder.find_collocations_hash(min_frequency=2)
    print("高频固定搭配:")
    for phrase, freq in collocations[:5]:
        print(f"  '{phrase}': {freq} 次")
    
    # 搜索特定搭配
    result = finder.search_collocation("climate change")
    print(f"\n搜索 'climate change': {result}")

def example_basic_index(texts):
    """基础反向索引使用示例"""
    print("\n=== 基础反向索引示例 ===")
    from basic_inverted_index import BasicInvertedIndex
    
    index = BasicInvertedIndex()
    index.build_index(texts)
    
    # 单词搜索
    docs = index.search_single_word("technology")
    print(f"搜索 'technology': 找到文档 {docs}")
    
    # 短语搜索
    docs = index.phrase_search("artificial intelligence")
    print(f"短语搜索 'artificial intelligence': 找到文档 {docs}")
    
    # 排序搜索
    results = index.ranked_search("economic development", top_k=3)
    print("排序搜索结果:")
    for doc_id, score in results:
        print(f"  文档 {doc_id}: 分数 {score:.4f}")

def example_lexical_index(texts):
    """词法分析反向索引使用示例"""
    print("\n=== 词法分析反向索引示例 ===")
    from lexical_inverted_index import LexicalInvertedIndex
    
    lexical_index = LexicalInvertedIndex()
    lexical_index.build_index(texts)
    
    # 词形还原搜索
    docs = lexical_index.search_by_lemma("technology")
    print(f"词形还原搜索 'technology': 找到文档 {docs}")
    
    # 高级搜索
    query = {
        'words': ['economic'],
        'lemmas': ['develop'],
        'operation': 'or'
    }
    results = lexical_index.advanced_search(query)
    print(f"高级搜索: 找到文档 {results}")

def example_vector_index(texts):
    """词向量反向索引使用示例"""
    print("\n=== 词向量反向索引示例 ===")
    from vector_inverted_index import VectorInvertedIndex
    
    vector_index = VectorInvertedIndex(vector_model_type="simple", vector_size=50)
    vector_index.build_index(texts)
    
    # 语义搜索
    results = vector_index.semantic_search("economic development", top_k=3)
    print("语义搜索结果:")
    for doc_id, score in results:
        print(f"  文档 {doc_id}: 相似度 {score:.4f}")
    
    # 相似词查找
    similar_words = vector_index.find_similar_words("technology", topn=3)
    print("'technology' 的相似词:")
    for word, similarity in similar_words:
        print(f"  {word}: {similarity:.4f}")

def example_regex_dfa():
    """正则表达式DFA使用示例"""
    print("\n=== 正则表达式DFA示例 ===")
    from regex_dfa import RegexDFA
    
    # 创建DFA
    dfa = RegexDFA("a*b+")
    
    # 测试字符串
    test_strings = ["b", "ab", "aab", "aaabbb", "abc", ""]
    print("测试正则表达式 'a*b+':")
    for test_str in test_strings:
        result = dfa.match(test_str)
        status = "匹配" if result else "不匹配"
        print(f"  '{test_str}': {status}")
    
    # 显示DFA信息
    info = dfa.get_dfa_info()
    print(f"\nDFA信息: {info['states_count']} 个状态")

def example_interpreter():
    """简单编程语言解释器使用示例"""
    print("\n=== 简单编程语言解释器示例 ===")
    from simple_interpreter import SimpleInterpreter
    
    interpreter = SimpleInterpreter()
    
    # 示例程序
    programs = [
        # 基本运算
        """
        let x = 10
        let y = 5
        let result = x * y + 2
        print result
        """,
        
        # 条件语句
        """
        let age = 25
        if age >= 18
            print "成年人"
        else
            print "未成年人"
        end
        """,
        
        # 字符串操作
        """
        let name = "Python"
        let greeting = "Hello, " + name + "!"
        print greeting
        """
    ]
    
    for i, program in enumerate(programs, 1):
        print(f"\n程序 {i}:")
        print("代码:", program.strip().replace('\n        ', '\n  '))
        print("输出:")
        try:
            interpreter.variables.clear()
            output = interpreter.interpret(program)
            for line in output:
                print(f"  {line}")
        except Exception as e:
            print(f"  错误: {e}")

def run_all_examples():
    """运行所有示例"""
    print("文本处理和语言处理系统 - 使用示例")
    print("=" * 50)
    
    # 1. 收集文本
    texts = example_text_collector()
    
    # 2. 固定搭配查找
    example_collocation_finder(texts)
    
    # 3. 基础反向索引
    example_basic_index(texts)
    
    # 4. 词法分析反向索引
    example_lexical_index(texts)
    
    # 5. 词向量反向索引
    example_vector_index(texts)
    
    # 6. 正则表达式DFA
    example_regex_dfa()
    
    # 7. 简单编程语言解释器
    example_interpreter()
    
    print("\n" + "=" * 50)
    print("所有示例运行完成！")

if __name__ == "__main__":
    run_all_examples()
