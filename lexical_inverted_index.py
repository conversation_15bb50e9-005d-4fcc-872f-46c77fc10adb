"""
词法分析反向索引模块 - 考虑词法分析的反向索引，支持多词搜索
"""
import re
import nltk
from collections import defaultdict
from typing import List, Dict, Set, Tuple, Optional
from basic_inverted_index import BasicInvertedIndex, Document, PostingList

# 下载必要的NLTK数据
try:
    nltk.data.find('tokenizers/punkt')
    from nltk.tokenize import word_tokenize
    from nltk.tag import pos_tag
    from nltk.stem import WordNetLemmatizer
    from nltk.corpus import stopwords, wordnet
    NLTK_AVAILABLE = True
except LookupError:
    print("下载NLTK数据...")
    try:
        nltk.download('punkt', quiet=True)
        nltk.download('averaged_perceptron_tagger', quiet=True)
        nltk.download('wordnet', quiet=True)
        nltk.download('stopwords', quiet=True)
        from nltk.tokenize import word_tokenize
        from nltk.tag import pos_tag
        from nltk.stem import WordNetLemmatizer
        from nltk.corpus import stopwords, wordnet
        NLTK_AVAILABLE = True
    except:
        print("NLTK数据下载失败，使用简化实现...")
        NLTK_AVAILABLE = False

class LexicalDocument(Document):
    """带词法分析的文档类"""
    def __init__(self, doc_id: int, content: str, title: str = ""):
        try:
            if NLTK_AVAILABLE:
                self.lemmatizer = WordNetLemmatizer()
                self.stop_words = set(stopwords.words('english'))
            else:
                raise Exception("NLTK not available")
        except:
            # 使用简化实现
            self.lemmatizer = None
            self.stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        super().__init__(doc_id, content, title)
        self.pos_tags = []
        self.lemmas = []
        self._perform_lexical_analysis()
    
    def _preprocess_content(self) -> List[str]:
        """预处理文档内容（重写父类方法）"""
        try:
            if NLTK_AVAILABLE:
                # 使用NLTK进行分词
                tokens = word_tokenize(self.content.lower())
                # 过滤标点符号和空字符串
                words = [token for token in tokens if token.isalpha() and len(token) > 1]
            else:
                raise Exception("NLTK not available")
        except:
            # 简化分词
            import re
            text = re.sub(r'[^\w\s]', ' ', self.content.lower())
            words = [word for word in text.split() if word.strip() and len(word) > 1]
        return words
    
    def _perform_lexical_analysis(self):
        """执行词法分析"""
        try:
            if NLTK_AVAILABLE:
                # 词性标注
                self.pos_tags = pos_tag(self.words)

                # 词形还原
                self.lemmas = []
                for word, pos in self.pos_tags:
                    # 转换POS标签为WordNet格式
                    wordnet_pos = self._get_wordnet_pos(pos)
                    if wordnet_pos:
                        lemma = self.lemmatizer.lemmatize(word, wordnet_pos)
                    else:
                        lemma = self.lemmatizer.lemmatize(word)
                    self.lemmas.append(lemma)
            else:
                raise Exception("NLTK not available")
        except:
            # 简化实现
            self.pos_tags = [(word, 'NN') for word in self.words]  # 假设都是名词
            self.lemmas = [self._simple_lemmatize(word) for word in self.words]
    
    def _simple_lemmatize(self, word: str) -> str:
        """简化的词形还原"""
        # 简单的规则
        if word.endswith('ies'):
            return word[:-3] + 'y'
        elif word.endswith('s') and not word.endswith('ss'):
            return word[:-1]
        elif word.endswith('ed'):
            return word[:-2]
        elif word.endswith('ing'):
            return word[:-3]
        return word

    def _get_wordnet_pos(self, treebank_tag: str) -> Optional[str]:
        """将TreeBank POS标签转换为WordNet POS标签"""
        if not NLTK_AVAILABLE:
            return None
        if treebank_tag.startswith('J'):
            return wordnet.ADJ
        elif treebank_tag.startswith('V'):
            return wordnet.VERB
        elif treebank_tag.startswith('N'):
            return wordnet.NOUN
        elif treebank_tag.startswith('R'):
            return wordnet.ADV
        else:
            return None

class LexicalPostingList(PostingList):
    """带词法信息的倒排列表"""
    def __init__(self):
        super().__init__()
        self.pos_info = defaultdict(list)  # {doc_id: [(position, pos_tag), ...]}
        self.lemma_info = defaultdict(list)  # {doc_id: [(position, lemma), ...]}
    
    def add_document_with_lexical_info(self, doc_id: int, positions: List[int], 
                                     pos_tags: List[str], lemmas: List[str]):
        """添加带词法信息的文档"""
        self.add_document(doc_id, positions)
        
        for i, pos in enumerate(positions):
            if i < len(pos_tags):
                self.pos_info[doc_id].append((pos, pos_tags[i]))
            if i < len(lemmas):
                self.lemma_info[doc_id].append((pos, lemmas[i]))

class LexicalInvertedIndex(BasicInvertedIndex):
    """词法分析反向索引"""
    def __init__(self):
        super().__init__()
        self.lemma_index = defaultdict(LexicalPostingList)  # 词形还原索引
        self.pos_index = defaultdict(LexicalPostingList)    # 词性索引
        try:
            if NLTK_AVAILABLE:
                self.stop_words = set(stopwords.words('english'))
                self.lemmatizer = WordNetLemmatizer()
            else:
                raise Exception("NLTK not available")
        except:
            # 使用简化实现
            self.stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
            self.lemmatizer = None
    
    def add_document(self, doc_id: int, content: str, title: str = ""):
        """添加文档到词法索引"""
        document = LexicalDocument(doc_id, content, title)
        self.documents[doc_id] = document
        self.total_documents += 1
        
        # 构建基础索引
        word_positions = defaultdict(list)
        lemma_positions = defaultdict(list)
        pos_positions = defaultdict(list)
        
        for position, (word, pos_tag) in enumerate(zip(document.words, [tag for _, tag in document.pos_tags])):
            # 原词索引
            word_positions[word].append(position)
            self.vocabulary.add(word)
            
            # 词形还原索引
            if position < len(document.lemmas):
                lemma = document.lemmas[position]
                lemma_positions[lemma].append(position)
            
            # 词性索引
            pos_positions[pos_tag].append(position)
        
        # 更新索引
        for word, positions in word_positions.items():
            if word not in self.index:
                self.index[word] = LexicalPostingList()
            
            # 获取对应的词性和词形
            word_pos_tags = [document.pos_tags[pos][1] for pos in positions if pos < len(document.pos_tags)]
            word_lemmas = [document.lemmas[pos] for pos in positions if pos < len(document.lemmas)]
            
            self.index[word].add_document_with_lexical_info(doc_id, positions, word_pos_tags, word_lemmas)
        
        # 更新词形还原索引
        for lemma, positions in lemma_positions.items():
            if lemma not in self.lemma_index:
                self.lemma_index[lemma] = LexicalPostingList()
            
            lemma_pos_tags = [document.pos_tags[pos][1] for pos in positions if pos < len(document.pos_tags)]
            lemma_lemmas = [lemma] * len(positions)
            
            self.lemma_index[lemma].add_document_with_lexical_info(doc_id, positions, lemma_pos_tags, lemma_lemmas)
        
        # 更新词性索引
        for pos_tag, positions in pos_positions.items():
            if pos_tag not in self.pos_index:
                self.pos_index[pos_tag] = LexicalPostingList()
            
            pos_words = [document.words[pos] for pos in positions if pos < len(document.words)]
            pos_lemmas = [document.lemmas[pos] for pos in positions if pos < len(document.lemmas)]
            
            self.pos_index[pos_tag].add_document_with_lexical_info(doc_id, positions, [pos_tag] * len(positions), pos_lemmas)
    
    def search_by_lemma(self, lemma: str) -> Set[int]:
        """按词形还原搜索"""
        lemma = lemma.lower()
        if lemma in self.lemma_index:
            return self.lemma_index[lemma].get_documents()
        return set()
    
    def search_by_pos(self, pos_tag: str) -> Set[int]:
        """按词性搜索"""
        if pos_tag in self.pos_index:
            return self.pos_index[pos_tag].get_documents()
        return set()
    
    def search_lemma_multiple_words(self, lemmas: List[str], operation: str = "and") -> Set[int]:
        """多词形还原搜索"""
        if not lemmas:
            return set()
        
        if operation == "and":
            result = self.search_by_lemma(lemmas[0])
            for lemma in lemmas[1:]:
                result = result.intersection(self.search_by_lemma(lemma))
                if not result:
                    break
        else:  # or
            result = set()
            for lemma in lemmas:
                result = result.union(self.search_by_lemma(lemma))
        
        return result
    
    def search_with_pos_filter(self, words: List[str], pos_tags: List[str], operation: str = "and") -> Set[int]:
        """带词性过滤的搜索"""
        # 先按词搜索
        if operation == "and":
            word_results = self.search_multiple_words_and(words)
        else:
            word_results = self.search_multiple_words_or(words)
        
        # 再按词性过滤
        pos_results = set()
        for pos_tag in pos_tags:
            pos_results = pos_results.union(self.search_by_pos(pos_tag))
        
        # 取交集
        return word_results.intersection(pos_results)
    
    def advanced_search(self, query: Dict) -> Set[int]:
        """高级搜索功能
        query格式: {
            'words': ['word1', 'word2'],
            'lemmas': ['lemma1', 'lemma2'],
            'pos_tags': ['NN', 'VB'],
            'exclude_stopwords': True,
            'operation': 'and'  # or 'or'
        }
        """
        results = []
        operation = query.get('operation', 'and')
        
        # 词搜索
        if 'words' in query and query['words']:
            words = query['words']
            if query.get('exclude_stopwords', False):
                words = [w for w in words if w.lower() not in self.stop_words]
            
            if operation == 'and':
                word_results = self.search_multiple_words_and(words)
            else:
                word_results = self.search_multiple_words_or(words)
            results.append(word_results)
        
        # 词形还原搜索
        if 'lemmas' in query and query['lemmas']:
            lemma_results = self.search_lemma_multiple_words(query['lemmas'], operation)
            results.append(lemma_results)
        
        # 词性搜索
        if 'pos_tags' in query and query['pos_tags']:
            pos_results = set()
            for pos_tag in query['pos_tags']:
                pos_results = pos_results.union(self.search_by_pos(pos_tag))
            results.append(pos_results)
        
        # 合并结果
        if not results:
            return set()
        
        if operation == 'and':
            final_result = results[0]
            for result_set in results[1:]:
                final_result = final_result.intersection(result_set)
        else:
            final_result = set()
            for result_set in results:
                final_result = final_result.union(result_set)
        
        return final_result
    
    def get_lexical_statistics(self) -> Dict:
        """获取词法统计信息"""
        total_lemmas = len(self.lemma_index)
        total_pos_tags = len(self.pos_index)
        
        # 统计词性分布
        pos_distribution = {}
        for pos_tag, posting_list in self.pos_index.items():
            pos_distribution[pos_tag] = posting_list.document_frequency
        
        return {
            "total_words": len(self.vocabulary),
            "total_lemmas": total_lemmas,
            "total_pos_tags": total_pos_tags,
            "pos_distribution": pos_distribution,
            "most_common_pos": max(pos_distribution.items(), key=lambda x: x[1]) if pos_distribution else None
        }
    
    def search_with_lexical_details(self, query: str, search_type: str = "advanced") -> Dict:
        """带词法分析的详细搜索"""
        words = query.lower().split()

        # 对查询进行词法分析
        if NLTK_AVAILABLE:
            query_pos_tags = pos_tag(words)
            query_lemmas = []
            for word, pos in query_pos_tags:
                wordnet_pos = self._get_wordnet_pos(pos)
                if wordnet_pos:
                    lemma = self.lemmatizer.lemmatize(word, wordnet_pos)
                else:
                    lemma = self.lemmatizer.lemmatize(word)
                query_lemmas.append(lemma)
        else:
            query_pos_tags = [(word, 'NN') for word in words]
            query_lemmas = [self._simple_lemmatize(word) for word in words]
        
        # 执行不同类型的搜索
        if search_type == "word":
            result_docs = self.search_multiple_words_and(words)
        elif search_type == "lemma":
            result_docs = self.search_lemma_multiple_words(query_lemmas)
        elif search_type == "advanced":
            # 综合搜索：词 + 词形还原
            word_results = self.search_multiple_words_and(words)
            lemma_results = self.search_lemma_multiple_words(query_lemmas)
            result_docs = word_results.union(lemma_results)
        else:
            result_docs = self.search_multiple_words_and(words)
        
        # 获取详细结果
        results = []
        for doc_id in result_docs:
            doc = self.documents[doc_id]
            
            # 计算相关性分数（考虑词形还原）
            word_score = sum(self.calculate_tf_idf(word, doc_id) for word in words)
            lemma_score = sum(self.calculate_tf_idf(lemma, doc_id) for lemma in query_lemmas if lemma in self.index)
            total_score = word_score + lemma_score * 0.8  # 词形还原权重稍低
            
            snippet = self._get_snippet(doc, words + query_lemmas)
            
            results.append({
                "doc_id": doc_id,
                "title": doc.title,
                "score": total_score,
                "snippet": snippet,
                "word_count": doc.word_count,
                "lexical_info": {
                    "query_pos_tags": query_pos_tags,
                    "query_lemmas": query_lemmas
                }
            })
        
        results.sort(key=lambda x: x["score"], reverse=True)
        
        return {
            "query": query,
            "search_type": search_type,
            "total_results": len(results),
            "results": results,
            "lexical_analysis": {
                "query_words": words,
                "query_pos_tags": query_pos_tags,
                "query_lemmas": query_lemmas
            }
        }
    
    def _simple_lemmatize(self, word: str) -> str:
        """简化的词形还原"""
        # 简单的规则
        if word.endswith('ies'):
            return word[:-3] + 'y'
        elif word.endswith('s') and not word.endswith('ss'):
            return word[:-1]
        elif word.endswith('ed'):
            return word[:-2]
        elif word.endswith('ing'):
            return word[:-3]
        return word

    def _get_wordnet_pos(self, treebank_tag: str) -> Optional[str]:
        """将TreeBank POS标签转换为WordNet POS标签"""
        if not NLTK_AVAILABLE:
            return None
        if treebank_tag.startswith('J'):
            return wordnet.ADJ
        elif treebank_tag.startswith('V'):
            return wordnet.VERB
        elif treebank_tag.startswith('N'):
            return wordnet.NOUN
        elif treebank_tag.startswith('R'):
            return wordnet.ADV
        else:
            return None

if __name__ == "__main__":
    # 示例使用
    from text_collector import TextCollector
    
    print("正在初始化词法分析反向索引...")
    
    # 收集文本
    collector = TextCollector()
    texts = collector.collect_texts(source="sample")
    
    # 构建词法索引
    lexical_index = LexicalInvertedIndex()
    lexical_index.build_index(texts)
    
    print("\n=== 词法分析搜索测试 ===")
    
    # 词形还原搜索
    print("\n1. 词形还原搜索 'technologies' (应该匹配 'technology'):")
    docs = lexical_index.search_by_lemma("technology")
    print(f"找到 {len(docs)} 个文档: {docs}")
    
    # 词性搜索
    print("\n2. 搜索所有名词 (NN, NNS):")
    nn_docs = lexical_index.search_by_pos("NN")
    nns_docs = lexical_index.search_by_pos("NNS")
    print(f"NN: {len(nn_docs)} 个文档, NNS: {len(nns_docs)} 个文档")
    
    # 高级搜索
    print("\n3. 高级搜索:")
    advanced_query = {
        'words': ['economic', 'growth'],
        'lemmas': ['economy', 'grow'],
        'pos_tags': ['NN', 'JJ'],
        'exclude_stopwords': True,
        'operation': 'or'
    }
    advanced_results = lexical_index.advanced_search(advanced_query)
    print(f"高级搜索结果: {len(advanced_results)} 个文档: {advanced_results}")
    
    # 词法分析详细搜索
    print("\n4. 词法分析详细搜索 'economic policies':")
    detailed_results = lexical_index.search_with_lexical_details("economic policies", "advanced")
    print(f"查询: {detailed_results['query']}")
    print(f"词法分析: {detailed_results['lexical_analysis']}")
    print(f"结果数量: {detailed_results['total_results']}")
    
    for result in detailed_results['results'][:2]:  # 显示前2个结果
        print(f"\n文档 {result['doc_id']} ({result['title']}):")
        print(f"分数: {result['score']:.4f}")
        print(f"片段: {result['snippet'][:100]}...")
    
    # 词法统计
    print("\n5. 词法统计信息:")
    stats = lexical_index.get_lexical_statistics()
    print(f"总词汇数: {stats['total_words']}")
    print(f"总词形数: {stats['total_lemmas']}")
    print(f"总词性标签数: {stats['total_pos_tags']}")
    print(f"最常见词性: {stats['most_common_pos']}")
    print(f"词性分布前5: {dict(list(sorted(stats['pos_distribution'].items(), key=lambda x: x[1], reverse=True))[:5])}")
