"""
词向量反向索引模块 - 基于Word2Vec/BERT的语义搜索反向索引
"""
import numpy as np
from collections import defaultdict
from typing import List, Dict, Set, Tuple, Optional
import pickle
import os
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from lexical_inverted_index import LexicalInvertedIndex

# 尝试导入gensim用于Word2Vec
try:
    from gensim.models import Word2Vec
    from gensim.models.keyedvectors import KeyedVectors
    GENSIM_AVAILABLE = True
except ImportError:
    print("警告: gensim未安装，将使用简化的词向量实现")
    GENSIM_AVAILABLE = False

# 尝试导入transformers用于BERT
try:
    from transformers import AutoTokenizer, AutoModel
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("警告: transformers未安装，将跳过BERT功能")
    TRANSFORMERS_AVAILABLE = False

class SimpleWordVectorizer:
    """简化的词向量实现（当gensim不可用时）"""
    def __init__(self, vector_size: int = 100):
        self.vector_size = vector_size
        self.word_vectors = {}
        self.vocabulary = set()
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000)
        self.is_trained = False
    
    def train(self, sentences: List[List[str]]):
        """训练简化的词向量"""
        print("使用简化方法训练词向量...")
        
        # 构建词汇表
        for sentence in sentences:
            self.vocabulary.update(sentence)
        
        # 使用TF-IDF作为基础特征
        corpus = [' '.join(sentence) for sentence in sentences]
        tfidf_matrix = self.tfidf_vectorizer.fit_transform(corpus)
        
        # 为每个词创建向量（基于TF-IDF特征的平均）
        feature_names = self.tfidf_vectorizer.get_feature_names_out()
        
        for word in self.vocabulary:
            if word in feature_names:
                word_idx = list(feature_names).index(word)
                # 使用随机向量加上一些TF-IDF信息
                vector = np.random.normal(0, 0.1, self.vector_size)
                vector[word_idx % self.vector_size] += 1.0  # 添加一些特征
                self.word_vectors[word] = vector / np.linalg.norm(vector)
            else:
                # 随机向量
                vector = np.random.normal(0, 0.1, self.vector_size)
                self.word_vectors[word] = vector / np.linalg.norm(vector)
        
        self.is_trained = True
        print(f"训练完成，词汇表大小: {len(self.vocabulary)}")
    
    def get_vector(self, word: str) -> Optional[np.ndarray]:
        """获取词向量"""
        return self.word_vectors.get(word.lower())
    
    def similarity(self, word1: str, word2: str) -> float:
        """计算词相似度"""
        vec1 = self.get_vector(word1)
        vec2 = self.get_vector(word2)
        
        if vec1 is not None and vec2 is not None:
            return float(cosine_similarity([vec1], [vec2])[0][0])
        return 0.0
    
    def most_similar(self, word: str, topn: int = 10) -> List[Tuple[str, float]]:
        """找到最相似的词"""
        word_vec = self.get_vector(word)
        if word_vec is None:
            return []
        
        similarities = []
        for other_word, other_vec in self.word_vectors.items():
            if other_word != word.lower():
                sim = float(cosine_similarity([word_vec], [other_vec])[0][0])
                similarities.append((other_word, sim))
        
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:topn]

class VectorInvertedIndex(LexicalInvertedIndex):
    """基于词向量的反向索引"""
    def __init__(self, vector_model_type: str = "simple", vector_size: int = 100):
        super().__init__()
        self.vector_model_type = vector_model_type
        self.vector_size = vector_size
        self.word_vectors = None
        self.document_vectors = {}  # {doc_id: vector}
        self.similarity_threshold = 0.6
        
        # 初始化词向量模型
        self._initialize_vector_model()
    
    def _initialize_vector_model(self):
        """初始化词向量模型"""
        if self.vector_model_type == "word2vec" and GENSIM_AVAILABLE:
            print("初始化Word2Vec模型...")
            self.word_vectors = None  # 将在训练时创建
        elif self.vector_model_type == "bert" and TRANSFORMERS_AVAILABLE:
            print("初始化BERT模型...")
            self.tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
            self.bert_model = AutoModel.from_pretrained('bert-base-uncased')
            self.bert_model.eval()
        else:
            print("使用简化词向量模型...")
            self.word_vectors = SimpleWordVectorizer(self.vector_size)
    
    def build_index(self, texts: List[str]):
        """构建向量索引"""
        print("正在构建词向量反向索引...")
        
        # 先构建基础索引
        super().build_index(texts)
        
        # 训练词向量模型
        self._train_vector_model(texts)
        
        # 构建文档向量
        self._build_document_vectors()
        
        print("词向量索引构建完成！")
    
    def _train_vector_model(self, texts: List[str]):
        """训练词向量模型"""
        # 准备训练数据
        sentences = []
        for text in texts:
            doc = self.documents[len(sentences)]
            sentences.append(doc.words)
        
        if self.vector_model_type == "word2vec" and GENSIM_AVAILABLE:
            print("训练Word2Vec模型...")
            self.word_vectors = Word2Vec(
                sentences=sentences,
                vector_size=self.vector_size,
                window=5,
                min_count=1,
                workers=1,
                sg=1  # Skip-gram
            )
        elif self.vector_model_type == "simple":
            self.word_vectors.train(sentences)
    
    def _build_document_vectors(self):
        """构建文档向量"""
        print("构建文档向量...")
        
        for doc_id, document in self.documents.items():
            if self.vector_model_type == "bert" and TRANSFORMERS_AVAILABLE:
                doc_vector = self._get_bert_document_vector(document.content)
            else:
                doc_vector = self._get_average_word_vector(document.words)
            
            if doc_vector is not None:
                self.document_vectors[doc_id] = doc_vector
    
    def _get_bert_document_vector(self, text: str) -> Optional[np.ndarray]:
        """获取BERT文档向量"""
        try:
            inputs = self.tokenizer(text, return_tensors='pt', truncation=True, 
                                  padding=True, max_length=512)
            
            with torch.no_grad():
                outputs = self.bert_model(**inputs)
                # 使用[CLS]标记的向量作为文档向量
                doc_vector = outputs.last_hidden_state[:, 0, :].numpy().flatten()
                return doc_vector
        except Exception as e:
            print(f"BERT向量化错误: {e}")
            return None
    
    def _get_average_word_vector(self, words: List[str]) -> Optional[np.ndarray]:
        """获取平均词向量作为文档向量"""
        vectors = []
        
        for word in words:
            if self.vector_model_type == "word2vec" and GENSIM_AVAILABLE:
                try:
                    vector = self.word_vectors.wv[word]
                    vectors.append(vector)
                except KeyError:
                    continue
            else:
                vector = self.word_vectors.get_vector(word)
                if vector is not None:
                    vectors.append(vector)
        
        if vectors:
            return np.mean(vectors, axis=0)
        return None
    
    def get_word_vector(self, word: str) -> Optional[np.ndarray]:
        """获取单词向量"""
        if self.vector_model_type == "word2vec" and GENSIM_AVAILABLE:
            try:
                return self.word_vectors.wv[word.lower()]
            except KeyError:
                return None
        else:
            return self.word_vectors.get_vector(word)
    
    def find_similar_words(self, word: str, topn: int = 10) -> List[Tuple[str, float]]:
        """找到相似词"""
        if self.vector_model_type == "word2vec" and GENSIM_AVAILABLE:
            try:
                return self.word_vectors.wv.most_similar(word.lower(), topn=topn)
            except KeyError:
                return []
        else:
            return self.word_vectors.most_similar(word, topn)
    
    def semantic_search(self, query: str, top_k: int = 5) -> List[Tuple[int, float]]:
        """语义搜索"""
        # 获取查询向量
        if self.vector_model_type == "bert" and TRANSFORMERS_AVAILABLE:
            query_vector = self._get_bert_document_vector(query)
        else:
            query_words = query.lower().split()
            query_vector = self._get_average_word_vector(query_words)
        
        if query_vector is None:
            return []
        
        # 计算与所有文档的相似度
        similarities = []
        for doc_id, doc_vector in self.document_vectors.items():
            similarity = cosine_similarity([query_vector], [doc_vector])[0][0]
            similarities.append((doc_id, float(similarity)))
        
        # 排序并返回top-k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def expanded_search(self, query: str, expansion_count: int = 3) -> Set[int]:
        """扩展搜索（使用相似词扩展查询）"""
        query_words = query.lower().split()
        expanded_words = set(query_words)
        
        # 为每个查询词找相似词
        for word in query_words:
            similar_words = self.find_similar_words(word, expansion_count)
            for similar_word, similarity in similar_words:
                if similarity > self.similarity_threshold:
                    expanded_words.add(similar_word)
        
        print(f"原查询词: {query_words}")
        print(f"扩展后词汇: {expanded_words}")
        
        # 使用扩展后的词汇进行搜索
        return self.search_multiple_words_or(list(expanded_words))
    
    def hybrid_search(self, query: str, weights: Dict[str, float] = None) -> List[Tuple[int, float]]:
        """混合搜索（结合关键词搜索和语义搜索）"""
        if weights is None:
            weights = {"keyword": 0.4, "semantic": 0.6}
        
        # 关键词搜索
        keyword_results = self.ranked_search(query, top_k=10)
        keyword_scores = {doc_id: score for doc_id, score in keyword_results}
        
        # 语义搜索
        semantic_results = self.semantic_search(query, top_k=10)
        semantic_scores = {doc_id: score for doc_id, score in semantic_results}
        
        # 合并分数
        all_docs = set(keyword_scores.keys()) | set(semantic_scores.keys())
        hybrid_scores = []
        
        for doc_id in all_docs:
            keyword_score = keyword_scores.get(doc_id, 0)
            semantic_score = semantic_scores.get(doc_id, 0)
            
            # 归一化分数
            if keyword_results:
                max_keyword = max(score for _, score in keyword_results)
                keyword_score = keyword_score / max_keyword if max_keyword > 0 else 0
            
            if semantic_results:
                max_semantic = max(score for _, score in semantic_results)
                semantic_score = semantic_score / max_semantic if max_semantic > 0 else 0
            
            # 加权合并
            hybrid_score = (weights["keyword"] * keyword_score + 
                          weights["semantic"] * semantic_score)
            
            hybrid_scores.append((doc_id, hybrid_score))
        
        # 排序
        hybrid_scores.sort(key=lambda x: x[1], reverse=True)
        return hybrid_scores
    
    def vector_search_with_details(self, query: str, search_type: str = "hybrid") -> Dict:
        """带详细信息的向量搜索"""
        if search_type == "semantic":
            results = self.semantic_search(query, top_k=10)
        elif search_type == "expanded":
            doc_ids = self.expanded_search(query)
            # 为扩展搜索结果计算语义分数
            results = []
            for doc_id in doc_ids:
                semantic_results = self.semantic_search(query, top_k=len(self.documents))
                for sid, score in semantic_results:
                    if sid == doc_id:
                        results.append((doc_id, score))
                        break
        else:  # hybrid
            results = self.hybrid_search(query)
        
        # 获取详细信息
        detailed_results = []
        for doc_id, score in results:
            doc = self.documents[doc_id]
            
            # 找相似词
            query_words = query.lower().split()
            similar_words_info = {}
            for word in query_words:
                similar_words = self.find_similar_words(word, 3)
                similar_words_info[word] = similar_words
            
            snippet = self._get_snippet(doc, query_words)
            
            detailed_results.append({
                "doc_id": doc_id,
                "title": doc.title,
                "score": score,
                "snippet": snippet,
                "word_count": doc.word_count,
                "vector_info": {
                    "search_type": search_type,
                    "similar_words": similar_words_info
                }
            })
        
        return {
            "query": query,
            "search_type": search_type,
            "total_results": len(detailed_results),
            "results": detailed_results,
            "vector_model": self.vector_model_type
        }
    
    def get_vector_statistics(self) -> Dict:
        """获取向量统计信息"""
        stats = {
            "vector_model_type": self.vector_model_type,
            "vector_size": self.vector_size,
            "documents_with_vectors": len(self.document_vectors),
            "total_documents": self.total_documents
        }
        
        if self.vector_model_type == "word2vec" and GENSIM_AVAILABLE and self.word_vectors:
            stats["vocabulary_size"] = len(self.word_vectors.wv.key_to_index)
        elif self.vector_model_type == "simple":
            stats["vocabulary_size"] = len(self.word_vectors.vocabulary)
        
        return stats

if __name__ == "__main__":
    # 示例使用
    from text_collector import TextCollector

    print("正在初始化词向量反向索引...")

    # 收集文本
    collector = TextCollector()
    texts = collector.collect_texts(source="sample")

    # 构建向量索引（使用简化模型，因为可能没有安装gensim/transformers）
    vector_index = VectorInvertedIndex(vector_model_type="simple", vector_size=50)
    vector_index.build_index(texts)

    print("\n=== 词向量搜索测试 ===")

    # 语义搜索
    print("\n1. 语义搜索 'economic development':")
    semantic_results = vector_index.semantic_search("economic development", top_k=3)
    for doc_id, score in semantic_results:
        print(f"文档 {doc_id}: 相似度 {score:.4f}")

    # 相似词查找
    print("\n2. 查找 'technology' 的相似词:")
    similar_words = vector_index.find_similar_words("technology", topn=5)
    for word, similarity in similar_words:
        print(f"  {word}: {similarity:.4f}")

    # 扩展搜索
    print("\n3. 扩展搜索 'artificial intelligence':")
    expanded_docs = vector_index.expanded_search("artificial intelligence", expansion_count=2)
    print(f"找到 {len(expanded_docs)} 个文档: {expanded_docs}")

    # 混合搜索
    print("\n4. 混合搜索 'climate change impact':")
    hybrid_results = vector_index.hybrid_search("climate change impact")
    for doc_id, score in hybrid_results[:3]:
        print(f"文档 {doc_id}: 混合分数 {score:.4f}")

    # 详细向量搜索
    print("\n5. 详细向量搜索结果:")
    detailed_results = vector_index.vector_search_with_details("environmental technology", "hybrid")
    print(f"查询: {detailed_results['query']}")
    print(f"搜索类型: {detailed_results['search_type']}")
    print(f"向量模型: {detailed_results['vector_model']}")
    print(f"结果数量: {detailed_results['total_results']}")

    for result in detailed_results['results'][:2]:
        print(f"\n文档 {result['doc_id']} ({result['title']}):")
        print(f"分数: {result['score']:.4f}")
        print(f"片段: {result['snippet'][:100]}...")
        if 'similar_words' in result['vector_info']:
            print(f"相似词: {result['vector_info']['similar_words']}")

    # 向量统计
    print("\n6. 向量统计信息:")
    vector_stats = vector_index.get_vector_statistics()
    for key, value in vector_stats.items():
        print(f"{key}: {value}")
