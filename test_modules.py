"""
模块测试脚本 - 验证所有模块的基本功能
"""
import sys
import traceback

def test_text_collector():
    """测试文本收集器"""
    try:
        from text_collector import TextCollector
        collector = TextCollector()
        texts = collector.collect_texts(source="sample")
        assert len(texts) > 0, "应该收集到文本"
        assert all(len(text.split()) > 50 for text in texts), "文本应该有足够的单词"
        return True, "文本收集器测试通过"
    except Exception as e:
        return False, f"文本收集器测试失败: {e}"

def test_collocation_finder():
    """测试固定搭配查找器"""
    try:
        from text_collector import TextCollector
        from collocation_finder import CollocationFinder
        
        collector = TextCollector()
        texts = collector.collect_texts(source="sample")
        
        finder = CollocationFinder()
        finder.analyze_texts(texts)
        
        # 测试哈希表查找
        collocations = finder.find_collocations_hash(min_frequency=1)
        assert len(collocations) > 0, "应该找到固定搭配"
        
        # 测试前缀树查找
        patterns = ["the global", "artificial intelligence"]
        trie_results = finder.find_collocations_trie(patterns)
        
        # 测试排序数组
        top_collocations = finder.get_top_collocations_sorted(5)
        
        return True, "固定搭配查找器测试通过"
    except Exception as e:
        return False, f"固定搭配查找器测试失败: {e}"

def test_basic_inverted_index():
    """测试基础反向索引"""
    try:
        from text_collector import TextCollector
        from basic_inverted_index import BasicInvertedIndex
        
        collector = TextCollector()
        texts = collector.collect_texts(source="sample")
        
        index = BasicInvertedIndex()
        index.build_index(texts)
        
        # 测试单词搜索
        docs = index.search_single_word("technology")
        assert isinstance(docs, set), "搜索结果应该是集合"
        
        # 测试AND搜索
        docs = index.search_multiple_words_and(["artificial", "intelligence"])
        
        # 测试短语搜索
        docs = index.phrase_search("artificial intelligence")
        
        # 测试排序搜索
        results = index.ranked_search("technology innovation", top_k=3)
        assert isinstance(results, list), "排序搜索结果应该是列表"
        
        return True, "基础反向索引测试通过"
    except Exception as e:
        return False, f"基础反向索引测试失败: {e}"

def test_lexical_inverted_index():
    """测试词法分析反向索引"""
    try:
        from text_collector import TextCollector
        from lexical_inverted_index import LexicalInvertedIndex
        
        collector = TextCollector()
        texts = collector.collect_texts(source="sample")
        
        lexical_index = LexicalInvertedIndex()
        lexical_index.build_index(texts)
        
        # 测试词形还原搜索
        docs = lexical_index.search_by_lemma("technology")
        assert isinstance(docs, set), "词形还原搜索结果应该是集合"
        
        # 测试词性搜索
        docs = lexical_index.search_by_pos("NN")
        
        # 测试高级搜索
        query = {
            'words': ['economic'],
            'operation': 'or'
        }
        results = lexical_index.advanced_search(query)
        assert isinstance(results, set), "高级搜索结果应该是集合"
        
        return True, "词法分析反向索引测试通过"
    except Exception as e:
        return False, f"词法分析反向索引测试失败: {e}"

def test_vector_inverted_index():
    """测试词向量反向索引"""
    try:
        from text_collector import TextCollector
        from vector_inverted_index import VectorInvertedIndex
        
        collector = TextCollector()
        texts = collector.collect_texts(source="sample")
        
        vector_index = VectorInvertedIndex(vector_model_type="simple", vector_size=50)
        vector_index.build_index(texts)
        
        # 测试语义搜索
        results = vector_index.semantic_search("economic development", top_k=3)
        assert isinstance(results, list), "语义搜索结果应该是列表"
        
        # 测试相似词查找
        similar_words = vector_index.find_similar_words("technology", topn=3)
        assert isinstance(similar_words, list), "相似词查找结果应该是列表"
        
        # 测试混合搜索
        hybrid_results = vector_index.hybrid_search("climate change")
        assert isinstance(hybrid_results, list), "混合搜索结果应该是列表"
        
        return True, "词向量反向索引测试通过"
    except Exception as e:
        return False, f"词向量反向索引测试失败: {e}"

def test_regex_dfa():
    """测试正则表达式DFA"""
    try:
        from regex_dfa import RegexDFA
        
        # 测试简单正则表达式
        test_cases = [
            ("a", "a", True),
            ("a", "b", False),
            ("a*", "", True),
            ("a*", "aaa", True),
            ("a*", "b", False),
            ("ab", "ab", True),
            ("ab", "a", False),
            ("a|b", "a", True),
            ("a|b", "b", True),
            ("a|b", "c", False),
        ]
        
        for regex, test_string, expected in test_cases:
            dfa = RegexDFA(regex)
            result = dfa.match(test_string)
            assert result == expected, f"正则表达式 '{regex}' 匹配 '{test_string}' 应该是 {expected}，但得到 {result}"
        
        # 测试DFA信息
        dfa = RegexDFA("a*")
        info = dfa.get_dfa_info()
        assert 'states_count' in info, "DFA信息应该包含状态数"
        
        return True, "正则表达式DFA测试通过"
    except Exception as e:
        return False, f"正则表达式DFA测试失败: {e}"

def test_simple_interpreter():
    """测试简单编程语言解释器"""
    try:
        from simple_interpreter import SimpleInterpreter
        
        interpreter = SimpleInterpreter()
        
        # 测试基本运算
        program1 = """
        let x = 10
        let y = 5
        let result = x + y
        print result
        """
        output1 = interpreter.interpret(program1)
        assert "15" in output1, "基本运算应该正确"
        
        # 测试条件语句
        interpreter.variables.clear()
        program2 = """
        let age = 25
        if age >= 18
            print "adult"
        else
            print "minor"
        end
        """
        output2 = interpreter.interpret(program2)
        assert "adult" in output2, "条件语句应该正确"
        
        return True, "简单编程语言解释器测试通过"
    except Exception as e:
        return False, f"简单编程语言解释器测试失败: {e}"

def run_all_tests():
    """运行所有测试"""
    tests = [
        ("文本收集器", test_text_collector),
        ("固定搭配查找器", test_collocation_finder),
        ("基础反向索引", test_basic_inverted_index),
        ("词法分析反向索引", test_lexical_inverted_index),
        ("词向量反向索引", test_vector_inverted_index),
        ("正则表达式DFA", test_regex_dfa),
        ("简单编程语言解释器", test_simple_interpreter),
    ]
    
    print("开始运行模块测试...")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n测试 {test_name}...")
        try:
            success, message = test_func()
            if success:
                print(f"✓ {message}")
                passed += 1
            else:
                print(f"✗ {message}")
                failed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试出现异常: {e}")
            traceback.print_exc()
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成！通过: {passed}, 失败: {failed}")
    
    if failed == 0:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print(f"⚠️  有 {failed} 个测试失败")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
