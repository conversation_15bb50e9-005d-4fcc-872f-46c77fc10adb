import bs4
import requests
import pandas as pd
import time
import random
import tkinter as tk
from tkinter import filedialog, ttk, scrolledtext
import threading
from fake_useragent import UserAgent
import os


class WebScraper:
    def __init__(self):
        # 创建GUI窗口
        self.root = tk.Tk()
        self.root.title("网页爬虫工具")
        self.root.geometry("800x600")
        self.root.configure(bg="#f0f0f0")

        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TLabel", font=("Arial", 11), background="#f0f0f0")
        self.style.configure("TButton", font=("Arial", 11))
        self.style.configure("TEntry", font=("Arial", 11))

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建输入区域
        input_frame = ttk.LabelFrame(main_frame, text="输入参数", padding=10)
        input_frame.pack(fill=tk.X, pady=10)

        # URL输入
        url_label = ttk.Label(input_frame, text="初始URL:")
        url_label.grid(row=0, column=0, sticky=tk.W, pady=5)
        self.url_entry = ttk.Entry(input_frame, width=80)
        self.url_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        self.url_entry.insert(0, "https://cn.bing.com/search?q=%e4%b8%96%e9%a3%8e%e6%97%a5%e4%b8%8bgb")

        # User-Agent输入
        ua_label = ttk.Label(input_frame, text="User-Agent:")
        ua_label.grid(row=1, column=0, sticky=tk.W, pady=5)
        self.ua_entry = ttk.Entry(input_frame, width=80)
        self.ua_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        self.ua_entry.insert(0,
                             "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0")

        # Cookie输入
        cookie_label = ttk.Label(input_frame, text="Cookie:")
        cookie_label.grid(row=2, column=0, sticky=tk.W, pady=5)
        self.cookie_entry = ttk.Entry(input_frame, width=80)
        self.cookie_entry.grid(row=2, column=1, sticky=tk.W, pady=5)

        # 爬取页数输入
        pages_label = ttk.Label(input_frame, text="爬取页数:")
        pages_label.grid(row=3, column=0, sticky=tk.W, pady=5)
        self.pages_entry = ttk.Entry(input_frame, width=10)
        self.pages_entry.grid(row=3, column=1, sticky=tk.W, pady=5)
        self.pages_entry.insert(0, "5")

        # 文件选择区域
        file_frame = ttk.Frame(main_frame)
        file_frame.pack(fill=tk.X, pady=10)

        self.file_path = tk.StringVar()
        file_label = ttk.Label(file_frame, text="保存文件:")
        file_label.grid(row=0, column=0, sticky=tk.W, pady=5)
        self.file_entry = ttk.Entry(file_frame, width=50, textvariable=self.file_path)
        self.file_entry.grid(row=0, column=1, sticky=tk.W, pady=5)

        browse_button = ttk.Button(file_frame, text="浏览...", command=self.browse_file)
        browse_button.grid(row=0, column=2, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=10)

        self.start_button = ttk.Button(control_frame, text="开始爬取", command=self.start_scraping)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(control_frame, text="停止爬取", command=self.stop_scraping, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # 进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=10)

        progress_label = ttk.Label(progress_frame, text="爬取进度:")
        progress_label.pack(side=tk.LEFT, padx=5)

        self.progress_bar = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL, length=600, mode='determinate')
        self.progress_bar.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="爬取日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, width=80, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 状态变量
        self.is_running = False
        self.total_pages = 0
        self.current_page = 0

    def browse_file(self):
        """选择保存的Excel文件"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path.set(file_path)

    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)

    def start_scraping(self):
        """开始爬取"""
        # 检查输入
        if not self.url_entry.get():
            self.log("错误: URL不能为空")
            return

        if not self.file_path.get():
            self.log("错误: 请选择保存文件")
            return

        try:
            self.total_pages = int(self.pages_entry.get())
            if self.total_pages <= 0:
                self.log("错误: 页数必须大于0")
                return
        except ValueError:
            self.log("错误: 页数必须是数字")
            return

        # 更新界面状态
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.is_running = True
        self.progress_bar["maximum"] = self.total_pages
        self.progress_bar["value"] = 0

        # 在新线程中启动爬虫
        scraping_thread = threading.Thread(target=self.scrape_pages)
        scraping_thread.daemon = True
        scraping_thread.start()

    def stop_scraping(self):
        """停止爬取"""
        self.is_running = False
        self.log("手动停止爬取")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def scrape_pages(self):
        """执行爬取操作"""
        try:
            base_url = self.url_entry.get()
            user_agent = self.ua_entry.get()
            cookie = self.cookie_entry.get()

            headers = {
                "user-agent": user_agent,
                "cookie": cookie
            }

            # 创建反爬措施
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0"
            ]

            # 准备存储数据
            all_data = []
            filtered_count = 0

            # 循环爬取每一页
            for page in range(self.total_pages):
                if not self.is_running:
                    break

                self.current_page = page + 1

                # 更新进度条
                self.root.after(0, lambda: self.progress_bar.config(value=self.current_page))

                # 构造URL
                current_url = base_url.replace('first=10',f'first={page*10}')

                # 使用随机User-Agent
                current_headers = headers.copy()
                if not user_agent:
                    current_headers["user-agent"] = random.choice(user_agents)

                self.log(f"正在爬取第 {self.current_page} 页: {current_url}")

                try:
                    # 添加随机延时，防止被封
                    sleep_time = random.uniform(1, 3)
                    time.sleep(sleep_time)

                    # 发送请求
                    response = requests.get(current_url, headers=current_headers, timeout=10)

                    # 检查响应状态
                    if response.status_code != 200:
                        self.log(f"警告: 请求返回状态码 {response.status_code}")
                        continue

                    # 解析HTML
                    soup = bs4.BeautifulSoup(response.text, 'html.parser')

                    # 提取数据
                    name_list = []
                    link_list = []

                    # 提取名称
                    names = soup.find_all('div', class_="tptt")
                    for i in names:
                        name_list.append(i.text.strip())

                    # 提取链接
                    links = soup.find_all('li', class_="b_algo")
                    for j in links:
                        try:
                            a_tag = j.find('a', target="_blank")
                            if a_tag and 'href' in a_tag.attrs:
                                link_list.append(a_tag['href'])
                        except (AttributeError, TypeError, KeyError) as e:
                            self.log(f"警告: 提取链接时出错 - {str(e)}")

                    # 整合数据并过滤
                    for name, link in zip(name_list, link_list):
                        if "晋江文学城" not in name:  # 过滤掉晋江文学城
                            all_data.append({"名称": name, "链接": link})
                        else:
                            filtered_count += 1

                    self.log(f"成功提取 {len(name_list)} 条数据，过滤 {filtered_count} 条数据")

                except requests.exceptions.RequestException as e:
                    self.log(f"错误: 请求失败 - {str(e)}")
                    # 等待更长时间后重试
                    time.sleep(random.uniform(5, 10))
                except Exception as e:
                    self.log(f"错误: 处理数据时出错 - {str(e)}")

            # 保存到Excel
            if all_data and self.is_running:
                df = pd.DataFrame(all_data)
                df.to_excel(self.file_path.get(), index=False)
                self.log(f"成功保存 {len(all_data)} 条数据到 {self.file_path.get()}")

            self.log("爬取完成")

        except Exception as e:
            self.log(f"错误: {str(e)}")

        finally:
            # 恢复界面状态
            self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
            self.is_running = False

    def run(self):
        """运行GUI程序"""
        self.root.mainloop()


if __name__ == "__main__":
    app = WebScraper()
    app.run()






