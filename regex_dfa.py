"""
正则表达式DFA模块 - 将正则表达式转换为确定有限自动机并进行字符串匹配
"""
from typing import Dict, Set, List, Optional, Tuple
from collections import defaultdict, deque
import re

class NFAState:
    """NFA状态类"""
    def __init__(self, state_id: int, is_final: bool = False):
        self.state_id = state_id
        self.is_final = is_final
        self.transitions = defaultdict(set)  # {symbol: {next_states}}
        self.epsilon_transitions = set()  # epsilon转换

class DFAState:
    """DFA状态类"""
    def __init__(self, state_id: int, nfa_states: Set[int], is_final: bool = False):
        self.state_id = state_id
        self.nfa_states = nfa_states  # 对应的NFA状态集合
        self.is_final = is_final
        self.transitions = {}  # {symbol: next_state_id}

class RegexDFA:
    """正则表达式DFA类"""
    def __init__(self, regex_pattern: str):
        self.regex_pattern = regex_pattern
        self.nfa_states = {}  # {state_id: NFAState}
        self.dfa_states = {}  # {state_id: DFAState}
        self.start_state = 0
        self.current_state_id = 0
        self.alphabet = set()
        
        # 构建DFA
        self._build_dfa()
    
    def _get_next_state_id(self) -> int:
        """获取下一个状态ID"""
        state_id = self.current_state_id
        self.current_state_id += 1
        return state_id
    
    def _build_nfa_from_regex(self, pattern: str) -> Tuple[int, int]:
        """从正则表达式构建NFA，返回(start_state, end_state)"""
        if not pattern:
            # 空模式
            start = self._get_next_state_id()
            end = self._get_next_state_id()
            self.nfa_states[start] = NFAState(start)
            self.nfa_states[end] = NFAState(end, is_final=True)
            self.nfa_states[start].epsilon_transitions.add(end)
            return start, end
        
        # 简化的正则表达式解析（支持基本操作）
        return self._parse_regex(pattern)
    
    def _parse_regex(self, pattern: str) -> Tuple[int, int]:
        """解析正则表达式（简化版本）"""
        if len(pattern) == 1:
            # 单个字符
            return self._create_char_nfa(pattern)
        
        # 处理连接（concatenation）
        if '|' not in pattern and '*' not in pattern and '+' not in pattern and '?' not in pattern:
            return self._create_concatenation_nfa(pattern)
        
        # 处理选择（alternation）
        if '|' in pattern:
            return self._create_alternation_nfa(pattern)
        
        # 处理Kleene星号
        if pattern.endswith('*'):
            base_pattern = pattern[:-1]
            base_start, base_end = self._parse_regex(base_pattern)
            return self._create_kleene_star_nfa(base_start, base_end)
        
        # 处理加号
        if pattern.endswith('+'):
            base_pattern = pattern[:-1]
            base_start, base_end = self._parse_regex(base_pattern)
            return self._create_plus_nfa(base_start, base_end)
        
        # 处理问号
        if pattern.endswith('?'):
            base_pattern = pattern[:-1]
            base_start, base_end = self._parse_regex(base_pattern)
            return self._create_optional_nfa(base_start, base_end)
        
        # 默认处理为连接
        return self._create_concatenation_nfa(pattern)
    
    def _create_char_nfa(self, char: str) -> Tuple[int, int]:
        """创建单字符NFA"""
        start = self._get_next_state_id()
        end = self._get_next_state_id()
        
        self.nfa_states[start] = NFAState(start)
        self.nfa_states[end] = NFAState(end, is_final=True)
        
        self.nfa_states[start].transitions[char].add(end)
        self.alphabet.add(char)
        
        return start, end
    
    def _create_concatenation_nfa(self, pattern: str) -> Tuple[int, int]:
        """创建连接NFA"""
        if len(pattern) == 1:
            return self._create_char_nfa(pattern)
        
        # 分割模式并递归构建
        first_char = pattern[0]
        rest_pattern = pattern[1:]
        
        first_start, first_end = self._create_char_nfa(first_char)
        rest_start, rest_end = self._parse_regex(rest_pattern)
        
        # 连接两个NFA
        self.nfa_states[first_end].is_final = False
        self.nfa_states[first_end].epsilon_transitions.add(rest_start)
        
        return first_start, rest_end
    
    def _create_alternation_nfa(self, pattern: str) -> Tuple[int, int]:
        """创建选择NFA (a|b)"""
        parts = pattern.split('|', 1)  # 只分割第一个|
        left_pattern = parts[0]
        right_pattern = parts[1]
        
        left_start, left_end = self._parse_regex(left_pattern)
        right_start, right_end = self._parse_regex(right_pattern)
        
        # 创建新的开始和结束状态
        new_start = self._get_next_state_id()
        new_end = self._get_next_state_id()
        
        self.nfa_states[new_start] = NFAState(new_start)
        self.nfa_states[new_end] = NFAState(new_end, is_final=True)
        
        # 连接
        self.nfa_states[new_start].epsilon_transitions.add(left_start)
        self.nfa_states[new_start].epsilon_transitions.add(right_start)
        
        self.nfa_states[left_end].is_final = False
        self.nfa_states[right_end].is_final = False
        self.nfa_states[left_end].epsilon_transitions.add(new_end)
        self.nfa_states[right_end].epsilon_transitions.add(new_end)
        
        return new_start, new_end
    
    def _create_kleene_star_nfa(self, start: int, end: int) -> Tuple[int, int]:
        """创建Kleene星号NFA (a*)"""
        new_start = self._get_next_state_id()
        new_end = self._get_next_state_id()
        
        self.nfa_states[new_start] = NFAState(new_start)
        self.nfa_states[new_end] = NFAState(new_end, is_final=True)
        
        # 连接
        self.nfa_states[new_start].epsilon_transitions.add(start)
        self.nfa_states[new_start].epsilon_transitions.add(new_end)
        
        self.nfa_states[end].is_final = False
        self.nfa_states[end].epsilon_transitions.add(start)
        self.nfa_states[end].epsilon_transitions.add(new_end)
        
        return new_start, new_end
    
    def _create_plus_nfa(self, start: int, end: int) -> Tuple[int, int]:
        """创建加号NFA (a+)"""
        # a+ = aa*
        star_start, star_end = self._create_kleene_star_nfa(start, end)
        
        new_start = self._get_next_state_id()
        self.nfa_states[new_start] = NFAState(new_start)
        self.nfa_states[new_start].epsilon_transitions.add(start)
        
        self.nfa_states[end].epsilon_transitions.add(star_start)
        
        return new_start, star_end
    
    def _create_optional_nfa(self, start: int, end: int) -> Tuple[int, int]:
        """创建可选NFA (a?)"""
        new_start = self._get_next_state_id()
        self.nfa_states[new_start] = NFAState(new_start)
        
        self.nfa_states[new_start].epsilon_transitions.add(start)
        self.nfa_states[new_start].epsilon_transitions.add(end)
        
        return new_start, end
    
    def _epsilon_closure(self, states: Set[int]) -> Set[int]:
        """计算epsilon闭包"""
        closure = set(states)
        stack = list(states)
        
        while stack:
            state = stack.pop()
            if state in self.nfa_states:
                for next_state in self.nfa_states[state].epsilon_transitions:
                    if next_state not in closure:
                        closure.add(next_state)
                        stack.append(next_state)
        
        return closure
    
    def _nfa_to_dfa(self, nfa_start: int):
        """将NFA转换为DFA"""
        # 初始DFA状态
        initial_closure = self._epsilon_closure({nfa_start})
        initial_is_final = any(self.nfa_states[s].is_final for s in initial_closure if s in self.nfa_states)
        
        dfa_start_id = 0
        self.dfa_states[dfa_start_id] = DFAState(dfa_start_id, initial_closure, initial_is_final)
        
        # 状态映射：nfa_states_set -> dfa_state_id
        state_mapping = {frozenset(initial_closure): dfa_start_id}
        unprocessed = [initial_closure]
        next_dfa_id = 1
        
        while unprocessed:
            current_nfa_states = unprocessed.pop(0)
            current_dfa_id = state_mapping[frozenset(current_nfa_states)]
            
            # 对每个字母计算转换
            for symbol in self.alphabet:
                next_nfa_states = set()
                
                for nfa_state in current_nfa_states:
                    if nfa_state in self.nfa_states:
                        next_nfa_states.update(self.nfa_states[nfa_state].transitions[symbol])
                
                if next_nfa_states:
                    next_closure = self._epsilon_closure(next_nfa_states)
                    next_closure_frozen = frozenset(next_closure)
                    
                    if next_closure_frozen not in state_mapping:
                        # 创建新的DFA状态
                        is_final = any(self.nfa_states[s].is_final for s in next_closure if s in self.nfa_states)
                        state_mapping[next_closure_frozen] = next_dfa_id
                        self.dfa_states[next_dfa_id] = DFAState(next_dfa_id, next_closure, is_final)
                        unprocessed.append(next_closure)
                        next_dfa_id += 1
                    
                    # 添加转换
                    target_dfa_id = state_mapping[next_closure_frozen]
                    self.dfa_states[current_dfa_id].transitions[symbol] = target_dfa_id
    
    def _build_dfa(self):
        """构建DFA"""
        print(f"正在为正则表达式 '{self.regex_pattern}' 构建DFA...")
        
        # 预处理正则表达式（简化版本）
        processed_pattern = self._preprocess_regex(self.regex_pattern)
        
        # 构建NFA
        nfa_start, nfa_end = self._build_nfa_from_regex(processed_pattern)
        
        # 转换为DFA
        self._nfa_to_dfa(nfa_start)
        
        print(f"DFA构建完成！")
        print(f"NFA状态数: {len(self.nfa_states)}")
        print(f"DFA状态数: {len(self.dfa_states)}")
        print(f"字母表: {self.alphabet}")
    
    def _preprocess_regex(self, pattern: str) -> str:
        """预处理正则表达式"""
        # 简单的预处理，移除不支持的特殊字符
        # 这里只是示例，实际实现会更复杂
        processed = pattern.replace('\\d', '[0-9]').replace('\\w', '[a-zA-Z0-9_]')
        return processed
    
    def match(self, input_string: str) -> bool:
        """检查字符串是否匹配正则表达式"""
        current_state = 0  # 从起始状态开始
        
        for char in input_string:
            if char not in self.alphabet:
                return False
            
            if current_state in self.dfa_states:
                if char in self.dfa_states[current_state].transitions:
                    current_state = self.dfa_states[current_state].transitions[char]
                else:
                    return False
            else:
                return False
        
        # 检查最终状态是否为接受状态
        return (current_state in self.dfa_states and 
                self.dfa_states[current_state].is_final)
    
    def get_dfa_info(self) -> Dict:
        """获取DFA信息"""
        transitions_info = {}
        for state_id, state in self.dfa_states.items():
            transitions_info[state_id] = {
                'is_final': state.is_final,
                'transitions': dict(state.transitions),
                'nfa_states': list(state.nfa_states)
            }
        
        return {
            'regex_pattern': self.regex_pattern,
            'alphabet': list(self.alphabet),
            'states_count': len(self.dfa_states),
            'start_state': 0,
            'states': transitions_info
        }
    
    def visualize_dfa(self) -> str:
        """可视化DFA（文本形式）"""
        result = [f"DFA for regex: {self.regex_pattern}"]
        result.append(f"Alphabet: {sorted(self.alphabet)}")
        result.append(f"States: {len(self.dfa_states)}")
        result.append("")
        
        for state_id in sorted(self.dfa_states.keys()):
            state = self.dfa_states[state_id]
            state_type = "FINAL" if state.is_final else "NORMAL"
            if state_id == 0:
                state_type = f"START, {state_type}"
            
            result.append(f"State {state_id} ({state_type}):")
            
            for symbol, next_state in sorted(state.transitions.items()):
                result.append(f"  --{symbol}--> State {next_state}")
            
            if not state.transitions:
                result.append("  (no transitions)")
            result.append("")
        
        return "\n".join(result)

if __name__ == "__main__":
    # 测试正则表达式DFA
    print("=== 正则表达式DFA测试 ===")
    
    # 测试用例
    test_cases = [
        ("a", ["a"], ["b", "aa", ""]),
        ("ab", ["ab"], ["a", "b", "abc", ""]),
        ("a|b", ["a", "b"], ["c", "ab", ""]),
        ("a*", ["", "a", "aa", "aaa"], ["b", "ab"]),
        ("a+", ["a", "aa", "aaa"], ["", "b", "ab"]),
        ("a?", ["", "a"], ["aa", "b"]),
    ]
    
    for regex, should_match, should_not_match in test_cases:
        print(f"\n--- 测试正则表达式: '{regex}' ---")
        
        try:
            dfa = RegexDFA(regex)
            
            print("应该匹配的字符串:")
            for test_string in should_match:
                result = dfa.match(test_string)
                status = "✓" if result else "✗"
                print(f"  '{test_string}': {status}")
            
            print("不应该匹配的字符串:")
            for test_string in should_not_match:
                result = dfa.match(test_string)
                status = "✗" if not result else "✓"
                print(f"  '{test_string}': {status}")
            
            # 显示DFA信息
            print("\nDFA信息:")
            dfa_info = dfa.get_dfa_info()
            print(f"  状态数: {dfa_info['states_count']}")
            print(f"  字母表: {dfa_info['alphabet']}")
            
        except Exception as e:
            print(f"构建DFA时出错: {e}")
    
    # 交互式测试
    print("\n=== 交互式测试 ===")
    print("输入正则表达式和测试字符串（输入 'quit' 退出）")
    
    while True:
        try:
            regex_input = input("\n请输入正则表达式: ").strip()
            if regex_input.lower() == 'quit':
                break
            
            if not regex_input:
                continue
            
            dfa = RegexDFA(regex_input)
            print(f"DFA构建成功！状态数: {len(dfa.dfa_states)}")
            
            while True:
                test_string = input("请输入测试字符串 (输入 'back' 返回): ").strip()
                if test_string.lower() == 'back':
                    break
                
                result = dfa.match(test_string)
                print(f"'{test_string}' {'匹配' if result else '不匹配'} 正则表达式 '{regex_input}'")
        
        except KeyboardInterrupt:
            print("\n程序退出")
            break
        except Exception as e:
            print(f"错误: {e}")
    
    print("测试完成！")
