"""
固定搭配查找模块 - 使用哈希表、前缀树、排序数组查找文本中的固定搭配
"""
import re
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Set
import heapq

class TrieNode:
    """前缀树节点"""
    def __init__(self):
        self.children = {}
        self.is_end = False
        self.frequency = 0
        self.phrases = []  # 存储以此节点结尾的短语

class Trie:
    """前缀树实现"""
    def __init__(self):
        self.root = TrieNode()
    
    def insert(self, phrase: str, frequency: int = 1):
        """插入短语到前缀树"""
        node = self.root
        words = phrase.lower().split()
        
        for word in words:
            if word not in node.children:
                node.children[word] = TrieNode()
            node = node.children[word]
        
        node.is_end = True
        node.frequency += frequency
        if phrase not in node.phrases:
            node.phrases.append(phrase)
    
    def search(self, phrase: str) -> bool:
        """搜索短语是否存在"""
        node = self.root
        words = phrase.lower().split()
        
        for word in words:
            if word not in node.children:
                return False
            node = node.children[word]
        
        return node.is_end
    
    def get_frequency(self, phrase: str) -> int:
        """获取短语频率"""
        node = self.root
        words = phrase.lower().split()
        
        for word in words:
            if word not in node.children:
                return 0
            node = node.children[word]
        
        return node.frequency if node.is_end else 0

class SortedArray:
    """排序数组实现（用于频率排序）"""
    def __init__(self):
        self.data = []  # [(frequency, phrase), ...]
    
    def insert(self, phrase: str, frequency: int):
        """插入短语和频率，保持排序"""
        item = (frequency, phrase)
        # 使用二分查找插入位置
        left, right = 0, len(self.data)
        while left < right:
            mid = (left + right) // 2
            if self.data[mid][0] < frequency:
                left = mid + 1
            else:
                right = mid
        self.data.insert(left, item)
    
    def get_top_k(self, k: int) -> List[Tuple[int, str]]:
        """获取频率最高的k个短语"""
        return self.data[-k:] if k <= len(self.data) else self.data[:]
    
    def search(self, phrase: str) -> int:
        """搜索短语频率"""
        for freq, p in self.data:
            if p == phrase:
                return freq
        return 0

class CollocationFinder:
    """固定搭配查找器"""
    def __init__(self):
        self.hash_table = defaultdict(int)  # 哈希表存储n-gram频率
        self.trie = Trie()  # 前缀树
        self.sorted_array = SortedArray()  # 排序数组
        self.texts = []
        self.bigrams = Counter()
        self.trigrams = Counter()
        self.fourgrams = Counter()
    
    def preprocess_text(self, text: str) -> List[str]:
        """预处理文本，返回单词列表"""
        # 转换为小写，移除标点符号
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        # 分割单词并过滤空字符串
        words = [word for word in text.split() if word.strip()]
        return words
    
    def extract_ngrams(self, words: List[str], n: int) -> List[str]:
        """提取n-gram"""
        if len(words) < n:
            return []
        return [' '.join(words[i:i+n]) for i in range(len(words) - n + 1)]
    
    def analyze_texts(self, texts: List[str]):
        """分析文本，提取固定搭配"""
        self.texts = texts
        all_words = []
        
        print("正在分析文本中的固定搭配...")
        
        # 处理每个文本
        for text in texts:
            words = self.preprocess_text(text)
            all_words.extend(words)
            
            # 提取2-gram, 3-gram, 4-gram
            bigrams = self.extract_ngrams(words, 2)
            trigrams = self.extract_ngrams(words, 3)
            fourgrams = self.extract_ngrams(words, 4)
            
            # 更新计数器
            self.bigrams.update(bigrams)
            self.trigrams.update(trigrams)
            self.fourgrams.update(fourgrams)
        
        # 合并所有n-gram
        all_ngrams = dict(self.bigrams) 
        all_ngrams.update(dict(self.trigrams))
        all_ngrams.update(dict(self.fourgrams))
        
        # 填充数据结构
        self._populate_data_structures(all_ngrams)
        
        print(f"分析完成！")
        print(f"找到 {len(self.bigrams)} 个二元组合")
        print(f"找到 {len(self.trigrams)} 个三元组合") 
        print(f"找到 {len(self.fourgrams)} 个四元组合")
    
    def _populate_data_structures(self, ngrams: Dict[str, int]):
        """填充哈希表、前缀树和排序数组"""
        # 过滤低频n-gram（频率至少为2）
        filtered_ngrams = {phrase: freq for phrase, freq in ngrams.items() if freq >= 2}
        
        for phrase, frequency in filtered_ngrams.items():
            # 哈希表
            self.hash_table[phrase] = frequency
            
            # 前缀树
            self.trie.insert(phrase, frequency)
            
            # 排序数组
            self.sorted_array.insert(phrase, frequency)
    
    def find_collocations_hash(self, min_frequency: int = 2) -> List[Tuple[str, int]]:
        """使用哈希表查找固定搭配"""
        print(f"\n=== 使用哈希表查找固定搭配（最小频率: {min_frequency}）===")
        
        collocations = [(phrase, freq) for phrase, freq in self.hash_table.items() 
                       if freq >= min_frequency]
        collocations.sort(key=lambda x: x[1], reverse=True)
        
        print(f"找到 {len(collocations)} 个固定搭配")
        return collocations
    
    def find_collocations_trie(self, patterns: List[str]) -> List[Tuple[str, int]]:
        """使用前缀树查找特定模式的固定搭配"""
        print(f"\n=== 使用前缀树查找固定搭配 ===")
        
        results = []
        for pattern in patterns:
            if self.trie.search(pattern):
                frequency = self.trie.get_frequency(pattern)
                results.append((pattern, frequency))
                print(f"找到模式 '{pattern}': 频率 {frequency}")
            else:
                print(f"未找到模式 '{pattern}'")
        
        return results
    
    def get_top_collocations_sorted(self, k: int = 10) -> List[Tuple[int, str]]:
        """使用排序数组获取频率最高的k个固定搭配"""
        print(f"\n=== 使用排序数组获取前 {k} 个高频固定搭配 ===")
        
        top_collocations = self.sorted_array.get_top_k(k)
        top_collocations.reverse()  # 从高到低排序
        
        for i, (freq, phrase) in enumerate(top_collocations, 1):
            print(f"{i}. '{phrase}': {freq} 次")
        
        return top_collocations
    
    def search_collocation(self, phrase: str) -> Dict[str, int]:
        """在所有数据结构中搜索固定搭配"""
        print(f"\n=== 搜索固定搭配: '{phrase}' ===")
        
        results = {
            'hash_table': self.hash_table.get(phrase, 0),
            'trie': self.trie.get_frequency(phrase),
            'sorted_array': self.sorted_array.search(phrase)
        }
        
        for method, frequency in results.items():
            print(f"{method}: {frequency} 次")
        
        return results
    
    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        return {
            'total_bigrams': len(self.bigrams),
            'total_trigrams': len(self.trigrams),
            'total_fourgrams': len(self.fourgrams),
            'frequent_collocations': len(self.hash_table),
            'unique_words': len(set(' '.join(self.texts).split()))
        }

if __name__ == "__main__":
    # 示例使用
    from text_collector import TextCollector
    
    # 收集文本
    collector = TextCollector()
    texts = collector.collect_texts(source="sample")
    
    # 分析固定搭配
    finder = CollocationFinder()
    finder.analyze_texts(texts)
    
    # 使用哈希表查找
    hash_collocations = finder.find_collocations_hash(min_frequency=2)
    print(f"\n前10个高频固定搭配:")
    for phrase, freq in hash_collocations[:10]:
        print(f"  '{phrase}': {freq} 次")
    
    # 使用前缀树查找特定模式
    patterns = ["artificial intelligence", "climate change", "economic growth", "global economy"]
    trie_results = finder.find_collocations_trie(patterns)
    
    # 使用排序数组获取top-k
    top_collocations = finder.get_top_collocations_sorted(10)
    
    # 搜索特定短语
    finder.search_collocation("climate change")
    
    # 显示统计信息
    stats = finder.get_statistics()
    print(f"\n=== 统计信息 ===")
    for key, value in stats.items():
        print(f"{key}: {value}")
