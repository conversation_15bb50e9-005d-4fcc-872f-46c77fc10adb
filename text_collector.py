"""
文本收集模块 - 从英文新闻网站收集文本
"""
import requests
from bs4 import BeautifulSoup
import re
import time
from typing import List, Dict

class TextCollector:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.collected_texts = []
    
    def collect_from_bbc(self, num_articles: int = 3) -> List[str]:
        """从BBC新闻收集文本"""
        print("正在从BBC News收集文本...")
        
        # BBC新闻主页
        url = "https://www.bbc.com/news"
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找文章链接
            article_links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                if '/news/' in href and href.startswith('/news/'):
                    full_url = f"https://www.bbc.com{href}"
                    article_links.append(full_url)
                    if len(article_links) >= num_articles:
                        break
            
            texts = []
            for i, article_url in enumerate(article_links[:num_articles]):
                print(f"收集第 {i+1} 篇文章...")
                text = self._extract_article_text(article_url)
                if text and len(text.split()) >= 50:  # 至少50个单词
                    texts.append(text)
                time.sleep(1)  # 避免请求过快
                
            return texts
            
        except Exception as e:
            print(f"从BBC收集文本时出错: {e}")
            return self._get_sample_texts()
    
    def _extract_article_text(self, url: str) -> str:
        """从单篇文章提取文本"""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 尝试多种可能的文章内容选择器
            content_selectors = [
                '[data-component="text-block"]',
                '.story-body__inner p',
                'article p',
                '.post-content p',
                'p'
            ]
            
            text_parts = []
            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    for elem in elements:
                        text = elem.get_text().strip()
                        if len(text) > 20:  # 过滤太短的段落
                            text_parts.append(text)
                    break
            
            full_text = ' '.join(text_parts)
            # 清理文本
            full_text = re.sub(r'\s+', ' ', full_text)
            full_text = re.sub(r'[^\w\s.,!?;:\-\'"()]', '', full_text)
            
            # 限制在300-400个单词
            words = full_text.split()
            if len(words) > 400:
                words = words[:400]
            elif len(words) < 300 and len(words) > 100:
                # 如果不够300个单词但超过100个，也接受
                pass
            
            return ' '.join(words)
            
        except Exception as e:
            print(f"提取文章文本时出错: {e}")
            return ""
    
    def _get_sample_texts(self) -> List[str]:
        """提供示例文本（当网络收集失败时使用）"""
        print("使用示例文本...")
        return [
            """
            The global economy continues to face unprecedented challenges as inflation rates 
            soar across major economies. Central banks worldwide are implementing aggressive 
            monetary policies to combat rising prices, but economists warn that these measures 
            could lead to recession. The Federal Reserve has raised interest rates multiple 
            times this year, while the European Central Bank follows suit with similar 
            strategies. Supply chain disruptions, energy costs, and geopolitical tensions 
            contribute to the complex economic landscape. Businesses struggle to maintain 
            profitability while consumers face increased living costs. Market volatility 
            reflects uncertainty about future economic conditions. Governments consider 
            fiscal interventions to support vulnerable populations and maintain economic 
            stability. International cooperation becomes crucial for addressing these 
            global challenges effectively.
            """,
            """
            Artificial intelligence technology advances rapidly, transforming industries 
            and reshaping the future of work. Machine learning algorithms demonstrate 
            remarkable capabilities in natural language processing, computer vision, and 
            decision-making tasks. Companies invest heavily in AI research and development, 
            seeking competitive advantages through automation and intelligent systems. 
            However, concerns about job displacement and ethical implications grow among 
            policymakers and society. Experts debate the need for regulation and governance 
            frameworks to ensure responsible AI development. Educational institutions 
            adapt curricula to prepare students for an AI-driven economy. Healthcare, 
            finance, and transportation sectors experience significant AI integration. 
            The technology promises improved efficiency and innovation while raising 
            questions about privacy, bias, and human autonomy in decision-making processes.
            """,
            """
            Climate change impacts accelerate globally, prompting urgent action from 
            governments and organizations worldwide. Rising temperatures, extreme weather 
            events, and sea-level increases threaten ecosystems and human communities. 
            Renewable energy adoption grows as countries commit to carbon neutrality goals. 
            Solar and wind power technologies become increasingly cost-effective alternatives 
            to fossil fuels. Environmental activists advocate for stronger climate policies 
            and corporate accountability. Scientists warn that immediate action is necessary 
            to prevent catastrophic consequences. International agreements like the Paris 
            Climate Accord guide global cooperation efforts. Green technology innovation 
            accelerates, creating new economic opportunities while addressing environmental 
            challenges. Public awareness and individual behavior changes contribute to 
            collective climate action initiatives across diverse communities and regions.
            """
        ]
    
    def collect_texts(self, source: str = "sample", num_articles: int = 3) -> List[str]:
        """收集文本的主要方法"""
        if source == "bbc":
            texts = self.collect_from_bbc(num_articles)
        else:
            texts = self._get_sample_texts()
        
        self.collected_texts = texts
        print(f"成功收集了 {len(texts)} 篇文本")
        for i, text in enumerate(texts):
            word_count = len(text.split())
            print(f"文本 {i+1}: {word_count} 个单词")
        
        return texts
    
    def save_texts(self, filename: str = "collected_texts.txt"):
        """保存收集的文本到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            for i, text in enumerate(self.collected_texts):
                f.write(f"=== 文本 {i+1} ===\n")
                f.write(text.strip())
                f.write("\n\n")
        print(f"文本已保存到 {filename}")

if __name__ == "__main__":
    collector = TextCollector()
    
    # 收集文本（默认使用示例文本，如需从网络收集请改为 "bbc"）
    texts = collector.collect_texts(source="sample")
    
    # 保存文本
    collector.save_texts()
    
    print("\n文本收集完成！")
    print("收集来源：示例文本（模拟BBC News等英文新闻网站内容）")
    print("如需从真实网站收集，请将 source 参数改为 'bbc'")
