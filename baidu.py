import bs4
import requests
from bs4 import BeautifulSoup
import xml

for page in range(2):

    url = f"https://www.baidu.com/s?wd=%E5%BC%BA%E5%8F%96%E4%BA%86%E6%AD%BB%E5%AF%B9%E5%A4%B4%E7%9A%84%E5%86%B0%E5%B1%B1%E7%94%B7%E5%8A%A9%E7%90%86&pn={page*10}&oq=%E5%BC%BA%E5%8F%96%E4%BA%86%E6%AD%BB%E5%AF%B9%E5%A4%B4%E7%9A%84%E5%86%B0%E5%B1%B1%E7%94%B7%E5%8A%A9%E7%90%86&ie=utf-8&usm=1&fenlei=256&rsv_idx=1&rsv_pq=954d17ea009d609a&rsv_t=13e4zjXQ0MFlwykof0PwbTN88Tz%2Fu5VwtE3k2t2%2Boc8QlPdUt2xrDryI0lg"
    headers = {
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
        "cookie": "BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; H_PS_PSSID=60271_61667_62325_63148_63242_63326_63353_63442_63507_63504_63498_63528_63539_63568_63564_63582_63578; PSINO=5; delPer=0; BDSVRTM=332; BD_CK_SAM=1; H_PS_645EC=3c11P8ootwyCqO56kxl3aqagkmowMcqhkQlwfK6cWj5zDzZTHPcIPrjuaBo; BA_HECTOR=05252g0104ah21218l81258g0l21a41k4oeiq24; BD_UPN=143254; BD_HOME=1; COOKIE_SESSION=72233_0_7_9_10_15_1_0_6_7_32_1_113439_0_0_0_1749396592_0_1749465339%7C9%230_0_1749465339%7C1; ZFY=zjHBRIsjGPbEwNHuVH1rgGd2dco098Rxb2JTDo8TF2s:C; H_WISE_SIDS=62325_63242_63268_63383_63442; BAIDUID=6ADC7EA3E416442D2C776DD0DB05A60A:FG=1; BIDUPSID=6ADC7EA3E416442D4CBBE02700091BD0; PSTM=1748854596"}
    response = requests.get(url, headers=headers)
    
    soup = bs4.BeautifulSoup(response.text, 'html.parser')

    name_list = []
    link_link = []

    name = soup.find_all('span', class_="cosc-source-text")
    for i in name:
        name_list.append(i.text)

    link = soup.find_all('h3', class_="t _sc-title_1g9za_66 title_2X7ZC struct-title_2gZl4")
    for j in link:
        j = j.find('a', target="_blank")
        link_link.append(j['href'])

    for name, link in zip(name_list, link_link):
        print(name, link)